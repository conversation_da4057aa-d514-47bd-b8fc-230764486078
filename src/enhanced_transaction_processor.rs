use serde_json::Value;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use crate::{SubscriptionManager, transaction_utils};

/// Enhanced transaction processor that only processes transactions for subscribed mints
pub struct EnhancedTransactionProcessor {
    pub subscription_manager: Arc<Mutex<SubscriptionManager>>,
    pub successful_tx_count: u64,
    pub total_tx_count: u64,
}

impl EnhancedTransactionProcessor {
    pub fn new(subscription_manager: Arc<Mutex<SubscriptionManager>>) -> Self {
        Self {
            subscription_manager,
            successful_tx_count: 0,
            total_tx_count: 0,
        }
    }

    /// Process a logsNotification message with enhanced mint-specific filtering
    pub async fn process_logs_notification(
        &mut self,
        json: &Value,
        window_map: &mut HashMap<String, crate::WindowStats>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        self.total_tx_count += 1;

        // Check if transaction was successful
        if !transaction_utils::is_transaction_successful(json) {
            return Ok(()); // Early exit for failed transactions
        }

        // Only process successful transactions from here
        self.successful_tx_count += 1;

        // Extract transaction signature
        let signature = transaction_utils::extract_signature(json);

        // Get list of currently subscribed mints to check if this transaction is relevant
        let subscribed_mints = {
            let manager = self.subscription_manager.lock().await;
            manager.get_active_mints()
        };

        // If no active subscriptions, skip processing
        if subscribed_mints.is_empty() {
            return Ok(());
        }

        // Check if this transaction involves any of our subscribed mints
        let mut relevant_mint = None;
        for mint in &subscribed_mints {
            if transaction_utils::transaction_involves_mint(json, mint) {
                relevant_mint = Some(mint.clone());
                break;
            }
        }

        // Only process transactions for subscribed mints
        if let Some(mint) = relevant_mint {
            self.process_mint_transaction(json, &mint, &signature, window_map).await?;
        }

        // Compact summary reporting every 25 successful transactions to reduce noise
        if self.successful_tx_count % 25 == 0 {
            self.print_summary_stats(window_map);
        }

        Ok(())
    }

    /// Process a transaction for a specific mint with clean, focused output
    async fn process_mint_transaction(
        &self,
        json: &Value,
        mint: &str,
        signature: &Option<String>,
        window_map: &mut HashMap<String, crate::WindowStats>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Extract logs for analysis
        let empty_logs = vec![];
        let logs = json.pointer("/params/result/value/logs")
            .and_then(|l| l.as_array())
            .unwrap_or(&empty_logs);

        // Enhanced buy/sell detection
        let transaction_type = transaction_utils::detect_buy_sell_type(logs);

        // Extract SOL amount
        let sol_amount = transaction_utils::extract_sol_amount(json)
            .unwrap_or(0.1); // Default to 0.1 SOL if can't extract

        // Convert SOL to lamports for consistent tracking
        let amount_lamports = (sol_amount * 1_000_000_000.0) as u64;

        // Get protocol information for context
        let protocols = transaction_utils::detect_protocols(logs);
        let protocol_str = if protocols.is_empty() {
            "Unknown".to_string()
        } else {
            protocols.join(",")
        };

        match transaction_type {
            Some(true) => {
                // Clean buy transaction output
                println!("🟢 BUY  | {} | {:.3} SOL | {} | {}",
                       protocol_str,
                       sol_amount,
                       mint,
                       signature.as_deref().unwrap_or("<no-sig>"));

                // Record buy in sliding window
                let stats = window_map.entry(mint.to_string()).or_insert_with(crate::WindowStats::new);
                stats.record_buy(amount_lamports);
            }
            Some(false) => {
                // Clean sell transaction output
                println!("🔴 SELL | {} | {:.3} SOL | {} | {}",
                       protocol_str,
                       sol_amount,
                       mint,
                       signature.as_deref().unwrap_or("<no-sig>"));

                // Record sell in sliding window
                let stats = window_map.entry(mint.to_string()).or_insert_with(crate::WindowStats::new);
                stats.record_sell(amount_lamports);
            }
            None => {
                // Skip unclear transactions silently to reduce noise
                return Ok(());
            }
        }

        // Compact window statistics (only show every 5th transaction to reduce noise)
        if (self.successful_tx_count % 5) == 0 {
            let stats = window_map.get_mut(mint).unwrap();
            let (b_cnt, b_sum, s_cnt, s_sum) = stats.totals();
            let b_sol = b_sum as f64 / 1_000_000_000.0;
            let s_sol = s_sum as f64 / 1_000_000_000.0;

            println!("📊 [{}] 60s window: {} buys ({:.3} SOL) | {} sells ({:.3} SOL)",
                     mint, b_cnt, b_sol, s_cnt, s_sol);
        }

        Ok(())
    }

    /// Print compact summary statistics
    fn print_summary_stats(&self, window_map: &HashMap<String, crate::WindowStats>) {
        let success_rate = (self.successful_tx_count as f64 / self.total_tx_count as f64) * 100.0;
        let active_mints = window_map.len();

        println!("📈 Stats: {} successful txs ({:.1}% success rate) | {} active mints",
               self.successful_tx_count, success_rate, active_mints);
    }
}

/// Helper function to validate that we're receiving transactions for the correct mints
pub async fn validate_subscription_effectiveness(
    subscription_manager: &Arc<Mutex<SubscriptionManager>>,
    processed_mints: &HashMap<String, crate::WindowStats>,
) {
    let subscribed_mints = {
        let manager = subscription_manager.lock().await;
        manager.get_active_mints()
    };

    println!("🔍 Subscription Validation:");
    println!("   • Subscribed mints: {}", subscribed_mints.len());
    println!("   • Mints with activity: {}", processed_mints.len());

    for mint in &subscribed_mints {
        if processed_mints.contains_key(mint) {
            println!("   ✅ {} - receiving transactions", mint);
        } else {
            println!("   ⚠️  {} - no transactions received yet", mint);
        }
    }

    // Check for unexpected mints (shouldn't happen with proper filtering)
    for mint in processed_mints.keys() {
        if !subscribed_mints.contains(mint) {
            println!("   ❌ {} - unexpected mint activity (not subscribed)", mint);
        }
    }
}
